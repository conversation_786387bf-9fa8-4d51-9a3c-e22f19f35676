<template>
  <div class="p-4">
    <h2>职业病结论录入表格样式测试</h2>
    
    <!-- 测试数据展示 -->
    <div class="mb-4">
      <a-button type="primary" @click="addTestData">添加测试数据</a-button>
      <a-button @click="clearData" class="ml-2">清空数据</a-button>
    </div>

    <!-- 表格容器 -->
    <div class="table-wrapper">
      <table class="conclusion-table">
        <!-- 表头 -->
        <thead>
          <tr>
            <th width="50">选择</th>
            <th width="80">序号</th>
            <th width="100">主/次</th>
            <th width="150">危害因素</th>
            <th width="120">结论 <span class="required">*</span></th>
            <th width="150">职业病</th>
            <th width="150">禁忌证</th>
            <th width="120">结论依据 <span class="required">*</span></th>
            <th width="200">处理意见</th>
            <th width="140">操作</th>
          </tr>
        </thead>
        <!-- 表体 -->
        <tbody>
          <ZyConclusionTableRow
            v-for="(record, index) in testData"
            :key="record.uuid"
            :record="record"
            :index="index"
            :selected="false"
            :loading="false"
            :saving="false"
            @edit="handleEdit"
            @save="handleSave"
            @cancel="handleCancel"
            @delete="handleDelete"
            @select="handleSelect"
          />
        </tbody>
      </table>
    </div>

    <!-- 空状态 -->
    <a-empty v-if="testData.length === 0" description="暂无测试数据">
      <a-button type="primary" @click="addTestData">添加测试数据</a-button>
    </a-empty>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { message } from 'ant-design-vue';
import { buildUUID } from '/@/utils/uuid';
import ZyConclusionTableRow from './components/ZyConclusionTableRow.vue';

const testData = ref<any[]>([]);

// 添加测试数据
const addTestData = () => {
  const newRecord = {
    uuid: buildUUID(),
    id: Date.now(),
    mainFlag: '1',
    riskCode: 'NOISE001',
    riskFactor: '噪声',
    riskFactorText: '噪声',
    conclusion: 'NORMAL',
    conclusionText: '未见异常',
    zyDisease: 'HEARING_LOSS',
    zyDiseaseText: '噪声性听力损失',
    zySymptom: 'HEARING_IMPAIR',
    zySymptomText: '听力障碍',
    according: '职业健康检查技术规范',
    advice: '建议定期复查，注意听力保护',
    editable: false,
    isNew: false,
    autoCreated: Math.random() > 0.5,
    manualCreated: Math.random() > 0.5
  };
  
  testData.value.push(newRecord);
  message.success('添加测试数据成功');
};

// 清空数据
const clearData = () => {
  testData.value = [];
  message.info('数据已清空');
};

// 事件处理
const handleEdit = (record: any) => {
  record.editable = true;
  message.info(`开始编辑: ${record.riskFactorText || record.riskFactor}`);
};

const handleSave = (record: any) => {
  record.editable = false;
  message.success(`保存成功: ${record.riskFactorText || record.riskFactor}`);
};

const handleCancel = (record: any) => {
  record.editable = false;
  message.info(`取消编辑: ${record.riskFactorText || record.riskFactor}`);
};

const handleDelete = (record: any) => {
  const index = testData.value.findIndex(item => item.uuid === record.uuid);
  if (index > -1) {
    testData.value.splice(index, 1);
    message.success('删除成功');
  }
};

const handleSelect = (record: any, checked: boolean) => {
  message.info(`${checked ? '选中' : '取消选中'}: ${record.riskFactorText || record.riskFactor}`);
};

// 初始化一些测试数据
addTestData();
addTestData();
</script>

<style lang="less" scoped>
.table-wrapper {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 16px;
}

.conclusion-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  thead {
    background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
    
    th {
      padding: 14px 8px;
      text-align: center;
      font-weight: 600;
      color: #262626;
      border-bottom: 2px solid #e6f7ff;
      border-right: 1px solid #f0f0f0;
      font-size: 13px;
      white-space: nowrap;
      
      &:last-child {
        border-right: none;
      }

      &:hover {
        background-color: #e6f7ff;
      }

      .required {
        color: #ff4d4f;
        font-weight: bold;
        font-size: 14px;
      }
    }
  }

  tbody {
    tr {
      transition: all 0.2s ease;
      
      &:nth-child(even) {
        background-color: #fafafa;
      }

      &:hover {
        background-color: #e6f7ff !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      td {
        border-right: 1px solid #f0f0f0;
        
        &:last-child {
          border-right: none;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .table-wrapper {
    overflow-x: auto;
  }
  
  .conclusion-table {
    min-width: 1000px;
  }
}
</style>
